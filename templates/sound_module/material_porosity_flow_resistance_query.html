{% extends "base.html" %}

{% block title %}材料孔隙率流阻查询 - NVH数据管理系统{% endblock %}

{% block head %}
<link href="{{ url_for('static', filename='css/sound_insulation.css') }}" rel="stylesheet">
<script src="{{ url_for('static', filename='js/material_porosity_flow_resistance.js') }}"></script>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">材料孔隙率流阻查询</h1>
</div>

<!-- 查询条件卡片 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="query-form">
            <div class="row">
                <!-- 零件选择 -->
                <div class="col-md-5">
                    <label for="part-select" class="form-label">零件选择</label>
                    <select class="form-select" id="part-select" multiple size="6">
                        <option value="">加载中...</option>
                    </select>
                    <div class="form-text">
                        <small>按住Ctrl键可多选零件</small>
                    </div>
                </div>
                
                <!-- 材料选择 -->
                <div class="col-md-5">
                    <label for="material-select" class="form-label">材料选择</label>
                    <select class="form-select" id="material-select" multiple size="6">
                        <option value="">请先选择零件</option>
                    </select>
                    <div class="form-text">
                        <small>按住Ctrl键可多选材料</small>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="col-md-2 d-flex flex-column justify-content-end">
                    <button type="button" class="btn btn-primary mb-2" id="query-btn" disabled>
                        <i class="fas fa-search me-1"></i>查询
                    </button>
                    <button type="button" class="btn btn-outline-secondary mb-2" id="reset-btn">
                        <i class="fas fa-undo me-1"></i>重置
                    </button>
                    <button type="button" class="btn btn-outline-success" id="export-btn" disabled>
                        <i class="fas fa-download me-1"></i>导出
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 结果展示区域 -->
<div class="card" id="results-card" style="display: none;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>孔隙率流阻查询结果
        </h5>
        <div class="d-flex align-items-center gap-3">
            <span class="badge bg-info" id="result-count">0 条记录</span>
        </div>
    </div>
    <div class="card-body">
        <!-- 数据表格 -->
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="data-table">
                <thead class="table-dark">
                    <tr>
                        <th>零件名称</th>
                        <th>材料组成</th>
                        <th>厚度(mm)</th>
                        <th>克重(g/m²)</th>
                        <th>密度(kg/m³)</th>
                        <th>孔隙率(%)</th>
                        <th>孔隙率偏差(%)</th>
                        <th>流阻率(Pa·s/m²)</th>
                        <th>流阻率偏差(Pa·s/m²)</th>
                        <th>测试机构</th>
                        <th>测试日期</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态生成表格内容 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 空状态提示 -->
<div class="card" id="empty-state" style="display: block;">
    <div class="card-body text-center py-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">请选择查询条件</h5>
        <p class="text-muted">选择零件和材料，点击"查询"按钮查看孔隙率流阻数据</p>
    </div>
</div>

<!-- 加载提示 -->
<div class="text-center py-4" id="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-2 text-muted">正在查询数据...</p>
</div>

<!-- 错误提示 -->
<div class="alert alert-danger" id="error-alert" style="display: none;">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <span id="error-message"></span>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // 初始化材料孔隙率流阻查询功能
    $(document).ready(function() {
        new MaterialPorosityFlowResistanceQuery();
    });
</script>
{% endblock %}
