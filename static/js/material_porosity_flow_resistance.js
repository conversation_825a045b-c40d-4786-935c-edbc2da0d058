/**
 * 材料孔隙率流阻查询功能
 */

class MaterialPorosityFlowResistanceQuery {
    constructor() {
        this.currentData = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadParts();
    }

    bindEvents() {
        // 零件选择变化事件
        $('#part-select').on('change', () => this.onPartChange());
        
        // 查询按钮点击事件
        $('#query-btn').on('click', () => this.queryData());
        
        // 重置按钮点击事件
        $('#reset-btn').on('click', () => this.resetForm());
        
        // 导出按钮点击事件
        $('#export-btn').on('click', () => this.exportData());
    }

    async loadParts() {
        try {
            const response = await fetch('/material_porosity_flow_resistance/api/parts');
            const result = await response.json();
            
            if (result.code === 200) {
                this.populatePartSelect(result.data);
            } else {
                this.showError('加载零件列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载零件列表失败: ' + error.message);
        }
    }

    populatePartSelect(parts) {
        const $select = $('#part-select');
        $select.empty();
        
        if (parts.length === 0) {
            $select.append('<option value="">暂无零件数据</option>');
            return;
        }
        
        parts.forEach(part => {
            $select.append(`<option value="${part.name}">${part.name}</option>`);
        });
        
        this.loadMaterials();
    }

    async onPartChange() {
        this.loadMaterials();
        this.updateButtonStates();
    }

    async loadMaterials() {
        const selectedParts = $('#part-select').val() || [];
        
        try {
            let url = '/material_porosity_flow_resistance/api/materials';
            if (selectedParts.length > 0) {
                const params = selectedParts.map(part => `part_names=${encodeURIComponent(part)}`).join('&');
                url += '?' + params;
            }
            
            const response = await fetch(url);
            const result = await response.json();
            
            if (result.code === 200) {
                this.populateMaterialSelect(result.data);
            } else {
                this.showError('加载材料列表失败: ' + result.message);
            }
        } catch (error) {
            this.showError('加载材料列表失败: ' + error.message);
        }
    }

    populateMaterialSelect(materials) {
        const $select = $('#material-select');
        $select.empty();
        
        if (materials.length === 0) {
            $select.append('<option value="">暂无材料数据</option>');
            return;
        }
        
        materials.forEach(material => {
            $select.append(`<option value="${material.name}">${material.name}</option>`);
        });
        
        this.updateButtonStates();
    }

    updateButtonStates() {
        const selectedParts = $('#part-select').val() || [];
        const selectedMaterials = $('#material-select').val() || [];
        const hasSelection = selectedParts.length > 0 || selectedMaterials.length > 0;
        
        $('#query-btn').prop('disabled', !hasSelection);
        $('#export-btn').prop('disabled', !this.currentData || this.currentData.length === 0);
    }

    async queryData() {
        const selectedParts = $('#part-select').val() || [];
        const selectedMaterials = $('#material-select').val() || [];
        
        if (selectedParts.length === 0 && selectedMaterials.length === 0) {
            this.showError('请至少选择一个零件或材料');
            return;
        }
        
        this.showLoading();
        this.hideError();
        
        try {
            let url = '/material_porosity_flow_resistance/api/query_data?';
            const params = [];
            
            selectedParts.forEach(part => {
                params.push(`part_names=${encodeURIComponent(part)}`);
            });
            
            selectedMaterials.forEach(material => {
                params.push(`material_names=${encodeURIComponent(material)}`);
            });
            
            url += params.join('&');
            
            const response = await fetch(url);
            const result = await response.json();
            
            if (result.code === 200) {
                this.currentData = result.data;
                this.displayResults(result.data);
                this.updateButtonStates();
            } else {
                this.showError(result.message);
                this.hideResults();
            }
        } catch (error) {
            this.showError('查询失败: ' + error.message);
            this.hideResults();
        } finally {
            this.hideLoading();
        }
    }

    displayResults(data) {
        this.hideEmptyState();
        this.showResults();
        
        // 更新记录数量
        $('#result-count').text(`${data.length} 条记录`);
        
        // 生成表格内容
        const $tbody = $('#data-table tbody');
        $tbody.empty();
        
        if (data.length === 0) {
            $tbody.append(`
                <tr>
                    <td colspan="11" class="text-center text-muted">暂无数据</td>
                </tr>
            `);
            return;
        }
        
        data.forEach(item => {
            const row = `
                <tr>
                    <td>${item.part_name || '-'}</td>
                    <td>${item.material_name || '-'}</td>
                    <td>${item.thickness || '-'}</td>
                    <td>${item.weight || '-'}</td>
                    <td>${item.density || '-'}</td>
                    <td>${item.porosity || '-'}</td>
                    <td>${item.porosity_deviation || '-'}</td>
                    <td>${item.flow_resistance || '-'}</td>
                    <td>${item.flow_resistance_deviation || '-'}</td>
                    <td>${item.test_institution || '-'}</td>
                    <td>${item.test_date || '-'}</td>
                </tr>
            `;
            $tbody.append(row);
        });
    }

    async exportData() {
        const selectedParts = $('#part-select').val() || [];
        const selectedMaterials = $('#material-select').val() || [];
        
        if (selectedParts.length === 0 && selectedMaterials.length === 0) {
            this.showError('请至少选择一个零件或材料');
            return;
        }
        
        try {
            let url = '/material_porosity_flow_resistance/api/export_csv?';
            const params = [];
            
            selectedParts.forEach(part => {
                params.push(`part_names=${encodeURIComponent(part)}`);
            });
            
            selectedMaterials.forEach(material => {
                params.push(`material_names=${encodeURIComponent(material)}`);
            });
            
            url += params.join('&');
            
            window.open(url, '_blank');
        } catch (error) {
            this.showError('导出失败: ' + error.message);
        }
    }

    resetForm() {
        $('#part-select').val([]);
        $('#material-select').val([]);
        this.currentData = null;
        this.hideResults();
        this.hideError();
        this.showEmptyState();
        this.loadMaterials();
        this.updateButtonStates();
    }

    showLoading() {
        $('#loading-indicator').show();
    }

    hideLoading() {
        $('#loading-indicator').hide();
    }

    showResults() {
        $('#results-card').show();
    }

    hideResults() {
        $('#results-card').hide();
    }

    showEmptyState() {
        $('#empty-state').show();
    }

    hideEmptyState() {
        $('#empty-state').hide();
    }

    showError(message) {
        $('#error-message').text(message);
        $('#error-alert').show();
    }

    hideError() {
        $('#error-alert').hide();
    }
}
